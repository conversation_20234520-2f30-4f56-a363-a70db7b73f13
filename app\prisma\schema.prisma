
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
    provider = "prisma-client-js"
    binaryTargets = ["native", "linux-musl-arm64-openssl-3.0.x"]
    output = "/home/<USER>/ai-travel-planner/app/node_modules/.prisma/client"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  password      String
  emailVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  accounts      Account[]
  sessions      Session[]
  itineraries   Itinerary[]
  preferences   UserPreferences?
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model UserPreferences {
  id                    String   @id @default(cuid())
  userId                String   @unique
  travelStyle           String[] // ["Relaxing", "Adventurous", "Cultural", "Family-Friendly"]
  accommodationPrefs    String[] // ["Hotel", "Hostel", "Vacation Rental"]
  budgetPreference      String   @default("Mid-Range") // "Budget", "Mid-Range", "Luxury"
  dietaryRestrictions   String?
  accessibilityNeeds    String?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Itinerary {
  id                String   @id @default(cuid())
  userId            String
  title             String
  destination       String
  startDate         DateTime
  endDate           DateTime
  numberOfPeople    Int
  budgetLevel       String
  travelStyle       String[]
  accommodationPrefs String[]
  dietaryRestrictions String?
  accessibilityNeeds String?
  aiGeneratedPlan   Json     // Store the AI-generated itinerary
  totalEstimatedCost Float?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}
