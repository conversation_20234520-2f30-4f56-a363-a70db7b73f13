
@tailwind base;
@tailwind components; 
@tailwind utilities;

@layer base {
  :root {
    /* Design System Colors based on design brief */
    --background: 249 250 251; /* #F9FAFB - Subtle off-white */
    --foreground: 17 24 39; /* #111827 - Near-black */
    --card: 255 255 255; /* #FFFFFF - Pure white */
    --card-foreground: 17 24 39;
    --popover: 255 255 255;
    --popover-foreground: 17 24 39;
    --primary: 59 130 246; /* #3B82F6 - Vibrant trustworthy blue */
    --primary-foreground: 255 255 255;
    --secondary: 243 244 246; /* #F3F4F6 - Light gray for sections */
    --secondary-foreground: 17 24 39;
    --muted: 243 244 246;
    --muted-foreground: 107 114 128; /* #6B7280 - Gray for secondary text */
    --accent: 59 130 246;
    --accent-foreground: 255 255 255;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 229 231 235;
    --input: 229 231 235;
    --ring: 59 130 246;
    --radius: 0.5rem;
  }

  .dark {
    --background: 15 23 42; /* Dark slate */
    --foreground: 248 250 252;
    --card: 30 41 59;
    --card-foreground: 248 250 252;
    --popover: 30 41 59;
    --popover-foreground: 248 250 252;
    --primary: 59 130 246;
    --primary-foreground: 255 255 255;
    --secondary: 51 65 85;
    --secondary-foreground: 248 250 252;
    --muted: 51 65 85;
    --muted-foreground: 148 163 184;
    --accent: 59 130 246;
    --accent-foreground: 255 255 255;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 51 65 85;
    --input: 51 65 85;
    --ring: 59 130 246;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Custom animations for travel theme */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes countUp {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-count-up {
  animation: countUp 2s ease-out forwards;
}

/* Travel-themed gradients */
.bg-travel-gradient {
  background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 50%, #1E3A8A 100%);
}

.bg-hero-gradient {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(30, 64, 175, 0.9) 100%);
}

/* Parallax effect */
.parallax {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-muted;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-primary/30 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-primary/50;
}
